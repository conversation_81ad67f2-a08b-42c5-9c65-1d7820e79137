import streamlit as st
import time
from email_logic import EmailAutomationSystem

# Configure Streamlit page
st.set_page_config(
    page_title="Email Automation System",
    page_icon="📧",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 10px;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .section-header {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #667eea;
        margin-bottom: 1rem;
    }
    
    .email-item {
        background: #f8f9fa;
        padding: 0.5rem;
        border-radius: 5px;
        margin-bottom: 0.25rem;
        border-left: 3px solid #667eea;
    }
    
    .success-message {
        background-color: #d4edda;
        color: #155724;
        padding: 1rem;
        border-radius: 5px;
        border: 1px solid #c3e6cb;
    }
    
    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 1rem;
        border-radius: 5px;
        border: 1px solid #f5c6cb;
    }
    
    .warning-message {
        background-color: #fff3cd;
        color: #856404;
        padding: 1rem;
        border-radius: 5px;
        border: 1px solid #ffeaa7;
    }
</style>
""", unsafe_allow_html=True)

# Initialize the email system
@st.cache_resource
def get_email_system():
    return EmailAutomationSystem()

def main():
    email_system = get_email_system()
    
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>📧 Email Automation System</h1>
        <p>Send personalized emails to multiple recipients with ease</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Initialize session state
    if 'selected_emails' not in st.session_state:
        st.session_state.selected_emails = []
    if 'email_sent' not in st.session_state:
        st.session_state.email_sent = False
    if 'results' not in st.session_state:
        st.session_state.results = None
    
    # Create two columns for the main content
    col1, col2 = st.columns([1, 1])
    
    # Left column - Compose Email
    with col1:
        st.markdown("""
        <div class="section-header">
            <h3>✉️ Compose Email</h3>
        </div>
        """, unsafe_allow_html=True)
        
        # Get sample data for pre-filling
        sample_data = email_system.get_sample_data()
        
        # Email composition form
        with st.form("email_compose_form", clear_on_submit=False):
            sender_email = st.text_input(
                "From Email:",
                value=sample_data['sender_email'],
                placeholder="<EMAIL>",
                help="Enter the sender's email address"
            )
            
            email_subject = st.text_input(
                "Subject:",
                value=sample_data['subject'],
                placeholder="Enter email subject",
                help="Enter the email subject line"
            )
            
            email_message = st.text_area(
                "Message:",
                value=sample_data['message'],
                height=200,
                placeholder="Enter your message here...",
                help="Enter the email message body"
            )
            
            # Store form values in session state
            if st.form_submit_button("Update Form", type="secondary"):
                st.session_state.sender_email = sender_email
                st.session_state.email_subject = email_subject
                st.session_state.email_message = email_message
                st.success("Form updated!")
    
    # Right column - Select Recipients
    with col2:
        st.markdown("""
        <div class="section-header">
            <h3>👥 Select Recipients</h3>
        </div>
        """, unsafe_allow_html=True)
        
        # Control buttons
        col2a, col2b = st.columns([1, 1])
        
        with col2a:
            if st.button("📋 Select All", use_container_width=True):
                st.session_state.selected_emails = email_system.get_predefined_emails()
                st.rerun()
        
        with col2b:
            if st.button("🗑️ Clear All", use_container_width=True):
                st.session_state.selected_emails = []
                st.rerun()
        
        # Selected count
        st.metric("Selected Recipients", len(st.session_state.selected_emails))
        
        # Email selection
        predefined_emails = email_system.get_predefined_emails()
        
        # Create a container with max height for scrolling
        with st.container():
            st.write("**Available Email Addresses:**")
            
            # Create checkboxes for each email
            for i, email in enumerate(predefined_emails):
                is_selected = email in st.session_state.selected_emails
                
                # Use a unique key for each checkbox
                checkbox_key = f"email_checkbox_{i}_{email}"
                
                if st.checkbox(
                    email,
                    value=is_selected,
                    key=checkbox_key,
                    help=f"Select {email} as recipient"
                ):
                    if email not in st.session_state.selected_emails:
                        st.session_state.selected_emails.append(email)
                else:
                    if email in st.session_state.selected_emails:
                        st.session_state.selected_emails.remove(email)
    
    # Send Email Section
    st.markdown("""
    <div class="section-header">
        <h3>🚀 Send Email Campaign</h3>
    </div>
    """, unsafe_allow_html=True)
    
    # Create columns for the send section
    send_col1, send_col2, send_col3 = st.columns([1, 2, 1])
    
    with send_col2:
        # Validate inputs before showing send button
        current_sender = st.session_state.get('sender_email', sender_email)
        current_subject = st.session_state.get('email_subject', email_subject)
        current_message = st.session_state.get('email_message', email_message)
        current_selected = st.session_state.selected_emails
        
        is_valid, error_message = email_system.validate_inputs(
            current_sender, current_subject, current_message, current_selected
        )
        
        # Show validation errors if any
        if not is_valid:
            st.error(f"❌ {error_message}")
        
        # Send button
        if st.button(
            "📤 Send Emails",
            use_container_width=True,
            disabled=not is_valid,
            type="primary"
        ):
            if is_valid:
                # Show progress
                progress_bar = st.progress(0)
                status_text = st.empty()
                
                try:
                    status_text.text("🚀 Sending emails...")
                    
                    # Send emails
                    results = email_system.send_emails_batch(
                        current_sender,
                        current_subject,
                        current_message,
                        current_selected
                    )
                    
                    # Update progress bar
                    for i, progress in enumerate(results['progress_updates']):
                        progress_bar.progress(int(progress))
                        status_text.text(f"📧 Sending email {i+1} of {len(current_selected)}...")
                        time.sleep(0.1)  # Small delay to show progress
                    
                    # Store results
                    st.session_state.results = results
                    st.session_state.email_sent = True
                    
                    # Clear progress indicators
                    progress_bar.empty()
                    status_text.empty()
                    
                    # Show results
                    message, message_type = email_system.format_results_message(results)
                    
                    if message_type == "success":
                        st.success(message)
                    elif message_type == "warning":
                        st.warning(message)
                    else:
                        st.error(message)
                    
                    # Show detailed results in expander
                    with st.expander("📊 Detailed Results", expanded=False):
                        st.write(f"**Total Emails:** {results['total_emails']}")
                        st.write(f"**Successful:** {results['success_count']}")
                        st.write(f"**Failed:** {results['failure_count']}")
                        
                        # Show individual results
                        for result in results['detailed_results']:
                            status_icon = "✅" if result['status'] == 'success' else "❌"
                            st.write(f"{status_icon} {result['email']} - {result['message']}")
                
                except Exception as e:
                    st.error(f"❌ An error occurred while sending emails: {str(e)}")
                    progress_bar.empty()
                    status_text.empty()
    
    # Sidebar with additional information
    st.sidebar.title("📋 Email System Info")
    st.sidebar.info(f"""
    **Available Recipients:** {len(predefined_emails)}
    **Selected Recipients:** {len(st.session_state.selected_emails)}
    **System Status:** Ready
    """)
    
    # Show current form values in sidebar
    st.sidebar.title("📝 Current Form Values")
    with st.sidebar.expander("View Form Data", expanded=False):
        st.write(f"**Sender:** {current_sender}")
        st.write(f"**Subject:** {current_subject}")
        st.write(f"**Message Length:** {len(current_message)} characters")
        st.write(f"**Recipients:** {len(current_selected)} selected")
    
    # Help section
    st.sidebar.title("❓ Help")
    with st.sidebar.expander("How to Use", expanded=False):
        st.write("""
        1. **Fill the form** with sender email, subject, and message
        2. **Select recipients** from the predefined list
        3. **Click 'Send Emails'** to start the campaign
        4. **Monitor progress** and view results
        
        **Note:** This is a simulation system for demonstration purposes.
        """)
    
    # Show recent results if available
    if st.session_state.email_sent and st.session_state.results:
        st.sidebar.title("📈 Last Campaign Results")
        results = st.session_state.results
        st.sidebar.metric("Success Rate", f"{(results['success_count']/results['total_emails']*100):.1f}%")
        st.sidebar.metric("Total Sent", results['success_count'])
        st.sidebar.metric("Failed", results['failure_count'])

if __name__ == "__main__":
    main()