import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
from Email_automation_system import EmailAutomationSystem
import json

class EmailAutomationGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Email Automation System")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # Initialize email system
        self.email_system = EmailAutomationSystem()
        
        # Create main interface
        self.create_widgets()
        
    def create_widgets(self):
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Tab 1: Configuration
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="Email Configuration")
        self.create_config_tab(config_frame)
        
        # Tab 2: Send Emails
        send_frame = ttk.Frame(notebook)
        notebook.add(send_frame, text="Send Bulk Emails")
        self.create_send_tab(send_frame)
        
        # Tab 3: Email Lists
        list_frame = ttk.Frame(notebook)
        notebook.add(list_frame, text="Email Lists")
        self.create_list_tab(list_frame)
        
        # Tab 4: Results
        results_frame = ttk.Frame(notebook)
        notebook.add(results_frame, text="Results")
        self.create_results_tab(results_frame)
    
    def create_config_tab(self, parent):
        """Create email configuration tab"""
        # Main frame
        main_frame = ttk.LabelFrame(parent, text="Email Account Configuration", padding=20)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Sender email
        ttk.Label(main_frame, text="Your Email Address:").grid(row=0, column=0, sticky='w', pady=5)
        self.sender_email_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.sender_email_var, width=40).grid(row=0, column=1, pady=5, padx=(10,0))
        
        # Sender password
        ttk.Label(main_frame, text="App Password:").grid(row=1, column=0, sticky='w', pady=5)
        self.sender_password_var = tk.StringVar()
        password_entry = ttk.Entry(main_frame, textvariable=self.sender_password_var, show="*", width=40)
        password_entry.grid(row=1, column=1, pady=5, padx=(10,0))
        
        # Provider selection
        ttk.Label(main_frame, text="Email Provider:").grid(row=2, column=0, sticky='w', pady=5)
        self.provider_var = tk.StringVar(value="gmail")
        provider_combo = ttk.Combobox(main_frame, textvariable=self.provider_var, 
                                    values=["gmail", "outlook", "yahoo"], state="readonly")
        provider_combo.grid(row=2, column=1, pady=5, padx=(10,0), sticky='w')
        
        # Save configuration button
        ttk.Button(main_frame, text="Save Configuration", 
                  command=self.save_config).grid(row=3, column=0, columnspan=2, pady=20)
        
        # Configuration status
        self.config_status_var = tk.StringVar(value="Configuration not saved")
        ttk.Label(main_frame, textvariable=self.config_status_var, 
                 foreground="red").grid(row=4, column=0, columnspan=2, pady=5)
        
        # Instructions
        instructions_frame = ttk.LabelFrame(parent, text="Setup Instructions", padding=10)
        instructions_frame.pack(fill='x', padx=10, pady=(0,10))
        
        instructions = """
        📧 GMAIL SETUP:
        1. Enable 2-Factor Authentication on your Google account
        2. Go to Google Account Settings > Security > App passwords
        3. Generate an app password for "Mail"
        4. Use this app password (not your regular password)
        
        📧 OUTLOOK/YAHOO:
        1. Enable 2-Factor Authentication
        2. Generate an app password from security settings
        3. Use the app password in the field above
        
        ⚠️  NEVER use your regular email password!
        """
        
        ttk.Label(instructions_frame, text=instructions, justify='left').pack()
    
    def create_send_tab(self, parent):
        """Create bulk email sending tab"""
        # Email content frame
        content_frame = ttk.LabelFrame(parent, text="Email Content", padding=10)
        content_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Subject
        ttk.Label(content_frame, text="Subject:").pack(anchor='w', pady=(0,5))
        self.subject_var = tk.StringVar()
        subject_entry = ttk.Entry(content_frame, textvariable=self.subject_var, width=80)
        subject_entry.pack(fill='x', pady=(0,10))
        
        # Message body
        ttk.Label(content_frame, text="Message Body:").pack(anchor='w', pady=(0,5))
        self.message_text = scrolledtext.ScrolledText(content_frame, height=12, width=80)
        self.message_text.pack(fill='both', expand=True, pady=(0,10))
        
        # Email options frame
        options_frame = ttk.LabelFrame(parent, text="Sending Options", padding=10)
        options_frame.pack(fill='x', padx=10, pady=(0,10))
        
        # Email list selection
        list_frame = ttk.Frame(options_frame)
        list_frame.pack(fill='x', pady=5)
        
        ttk.Label(list_frame, text="Recipients:").pack(side='left')
        self.recipient_type_var = tk.StringVar(value="predefined")
        ttk.Radiobutton(list_frame, text="Predefined List (20 emails)", 
                       variable=self.recipient_type_var, value="predefined").pack(side='left', padx=10)
        ttk.Radiobutton(list_frame, text="Custom List", 
                       variable=self.recipient_type_var, value="custom").pack(side='left', padx=10)
        
        # Batch settings
        batch_frame = ttk.Frame(options_frame)
        batch_frame.pack(fill='x', pady=5)
        
        ttk.Label(batch_frame, text="Batch Size:").pack(side='left')
        self.batch_size_var = tk.StringVar(value="10")
        ttk.Entry(batch_frame, textvariable=self.batch_size_var, width=10).pack(side='left', padx=10)
        
        ttk.Label(batch_frame, text="Delay (seconds):").pack(side='left', padx=(20,0))
        self.delay_var = tk.StringVar(value="2")
        ttk.Entry(batch_frame, textvariable=self.delay_var, width=10).pack(side='left', padx=10)
        
        # Send button and progress
        send_frame = ttk.Frame(options_frame)
        send_frame.pack(fill='x', pady=10)
        
        self.send_button = ttk.Button(send_frame, text="Send Bulk Emails", 
                                     command=self.send_emails_thread)
        self.send_button.pack(side='left')
        
        self.progress_bar = ttk.Progressbar(send_frame, mode='indeterminate')
        self.progress_bar.pack(side='left', fill='x', expand=True, padx=10)
        
        # Status
        self.send_status_var = tk.StringVar(value="Ready to send emails")
        ttk.Label(options_frame, textvariable=self.send_status_var).pack(pady=5)
    
    def create_list_tab(self, parent):
        """Create email list management tab"""
        # Predefined emails frame
        predefined_frame = ttk.LabelFrame(parent, text="Predefined Email List (20 emails)", padding=10)
        predefined_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.predefined_listbox = tk.Listbox(predefined_frame, height=8)
        self.predefined_listbox.pack(fill='both', expand=True)
        
        # Load predefined emails
        for email in self.email_system.get_predefined_emails():
            self.predefined_listbox.insert(tk.END, email)
        
        # Custom emails frame
        custom_frame = ttk.LabelFrame(parent, text="Custom Email List", padding=10)
        custom_frame.pack(fill='both', expand=True, padx=10, pady=(0,10))
        
        # Custom email input
        input_frame = ttk.Frame(custom_frame)
        input_frame.pack(fill='x', pady=(0,10))
        
        self.custom_email_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.custom_email_var, width=50).pack(side='left')
        ttk.Button(input_frame, text="Add Email", command=self.add_custom_email).pack(side='left', padx=10)
        
        # Custom emails listbox
        listbox_frame = ttk.Frame(custom_frame)
        listbox_frame.pack(fill='both', expand=True)
        
        self.custom_listbox = tk.Listbox(listbox_frame, height=8)
        self.custom_listbox.pack(side='left', fill='both', expand=True)
        
        scrollbar = ttk.Scrollbar(listbox_frame, orient="vertical", command=self.custom_listbox.yview)
        scrollbar.pack(side='right', fill='y')
        self.custom_listbox.configure(yscrollcommand=scrollbar.set)
        
        # Remove button
        ttk.Button(custom_frame, text="Remove Selected", 
                  command=self.remove_custom_email).pack(pady=5)
    
    def create_results_tab(self, parent):
        """Create results display tab"""
        results_frame = ttk.LabelFrame(parent, text="Email Sending Results", padding=10)
        results_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.results_text = scrolledtext.ScrolledText(results_frame, height=25, width=80)
        self.results_text.pack(fill='both', expand=True)
        
        # Clear button
        ttk.Button(results_frame, text="Clear Results", 
                  command=lambda: self.results_text.delete(1.0, tk.END)).pack(pady=5)
    
    def save_config(self):
        """Save email configuration"""
        try:
            self.email_system.setup_email_config(
                sender_email=self.sender_email_var.get(),
                sender_password=self.sender_password_var.get(),
                provider=self.provider_var.get()
            )
            self.config_status_var.set("✓ Configuration saved successfully")
            self.root.nametowidget(self.config_status_var._name).configure(foreground="green")
        except Exception as e:
            self.config_status_var.set(f"✗ Error: {str(e)}")
            self.root.nametowidget(self.config_status_var._name).configure(foreground="red")
    
    def add_custom_email(self):
        """Add email to custom list"""
        email = self.custom_email_var.get().strip()
        if email and "@" in email:
            self.custom_listbox.insert(tk.END, email)
            self.custom_email_var.set("")
        else:
            messagebox.showerror("Invalid Email", "Please enter a valid email address")
    
    def remove_custom_email(self):
        """Remove selected email from custom list"""
        selection = self.custom_listbox.curselection()
        if selection:
            self.custom_listbox.delete(selection[0])
    
    def get_custom_emails(self):
        """Get all emails from custom listbox"""
        return [self.custom_listbox.get(i) for i in range(self.custom_listbox.size())]
    
    def send_emails_thread(self):
        """Start email sending in separate thread"""
        # Disable send button
        self.send_button.config(state='disabled')
        self.progress_bar.start()
        
        # Start thread
        thread = threading.Thread(target=self.send_emails_worker)
        thread.daemon = True
        thread.start()
    
    def send_emails_worker(self):
        """Worker function for sending emails"""
        try:
            # Validate inputs
            if not self.subject_var.get().strip():
                raise ValueError("Subject cannot be empty")
            
            message_body = self.message_text.get(1.0, tk.END).strip()
            if not message_body:
                raise ValueError("Message body cannot be empty")
            
            # Get recipient emails
            use_predefined = self.recipient_type_var.get() == "predefined"
            custom_emails = self.get_custom_emails() if not use_predefined else None
            
            if not use_predefined and not custom_emails:
                raise ValueError("No custom emails provided")
            
            # Update status
            self.root.after(0, lambda: self.send_status_var.set("Sending emails..."))
            
            # Send emails
            results = self.email_system.send_bulk_emails(
                subject=self.subject_var.get(),
                message_body=message_body,
                recipient_emails=custom_emails,
                use_predefined=use_predefined,
                batch_size=int(self.batch_size_var.get()),
                delay_between_batches=int(self.delay_var.get())
            )
            
            # Display results
            self.root.after(0, lambda: self.display_results(results))
            
        except Exception as e:
            self.root.after(0, lambda: self.send_status_var.set(f"Error: {str(e)}"))
        finally:
            # Re-enable button and stop progress
            self.root.after(0, lambda: self.send_button.config(state='normal'))
            self.root.after(0, lambda: self.progress_bar.stop())
    
    def display_results(self, results):
        """Display email sending results"""
        self.send_status_var.set(f"Completed: {results['successful']} sent, {results['failed']} failed")
        
        # Format results
        result_text = f"""
EMAIL SENDING RESULTS
{'='*50}
Total Emails: {results['total_emails']}
Successful: {results['successful']}
Failed: {results['failed']}
Start Time: {results['start_time']}
End Time: {results['end_time']}

"""
        
        if results.get('failed_emails'):
            result_text += "FAILED EMAILS:\n"
            for failed in results['failed_emails']:
                result_text += f"  ✗ {failed['email']}: {failed['error']}\n"
        
        result_text += "\n" + "="*50 + "\n\n"
        
        # Insert results at the beginning
        self.results_text.insert(1.0, result_text)

def main():
    root = tk.Tk()
    app = EmailAutomationGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()