import{r as l,D as s,j as m,bu as V,ce as x}from"./index.DvRPFfw6.js";import{a as U}from"./useBasicWidgetState.CUSYQZpm.js";import"./FormClearHelper.BLEIUk6L.js";const h=(t,e)=>t.getStringValue(e),v=t=>t.options.length===0||s(t.default)?null:t.options[t.default],w=t=>t.rawValue??null,y=(t,e,o,a)=>{e.setStringValue(t,o.value,{fromUi:o.fromUi},a)},C=({disabled:t,element:e,widgetMgr:o,fragmentId:a})=>{const{options:i,help:u,label:n,labelVisibility:c,placeholder:p,acceptNewOptions:f}=e,[g,r]=U({getStateFromWidgetMgr:h,getDefaultStateFromProto:v,getCurrStateFromProto:w,updateWidgetMgrState:y,element:e,widgetMgr:o,fragmentId:a}),b=l.useCallback(d=>{r({value:d,fromUi:!0})},[r]),S=s(e.default)&&!t;return m(x,{label:n,labelVisibility:V(c?.value),options:i,disabled:t,onChange:b,value:g,help:u,placeholder:p,clearable:S,acceptNewOptions:f??!1})},P=l.memo(C);export{P as default};
