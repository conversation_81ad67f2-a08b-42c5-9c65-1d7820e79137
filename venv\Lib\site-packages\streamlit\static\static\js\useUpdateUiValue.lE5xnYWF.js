import{r as p,cy as a}from"./index.DvRPFfw6.js";import{i as b}from"./inputUtils.CptNuJwn.js";function i({formId:r,maxChars:s,setDirty:n,setUiValue:e,setValueWithSource:f,additionalAction:t}){return p.useCallback(u=>{const{value:o}=u.target;s!==0&&o.length>s||(n(!0),e(o),a({formId:r})&&f({value:o,fromUi:!0}),t&&t())},[r,s,n,e,f,t])}function F(r,s,n,e,f,t=!1){return p.useCallback(u=>{const o=t?u.metaKey||u.ctrlKey:!0;!b(u)||!o||(u.preventDefault(),n&&s(),e.allowFormEnterToSubmit(r)&&e.submitForm(r,f))},[r,f,n,s,e,t])}function K(r,s,n,e){p.useEffect(()=>{e||r!==s&&n(r)},[r,s,e,n])}export{i as a,F as b,K as u};
