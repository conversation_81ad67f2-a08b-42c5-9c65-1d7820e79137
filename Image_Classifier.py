"""
Face Expression Classifier using Teachable Machine Model
Requires: pip install tensorflow opencv-python requests numpy pillow
"""

import cv2
import numpy as np
import tensorflow as tf
import requests
import json
from PIL import Image
import time

class TeachableMachineClassifier:
    def __init__(self, model_url):
        """
        Initialize the classifier with a Teachable Machine model URL
        
        Args:
            model_url (str): The shareable URL from Teachable Machine
        """
        self.model_url = model_url.rstrip('/') + '/'
        self.model = None
        self.class_names = []
        self.load_model()
    
    def load_model(self):
        """Load the model and metadata from Teachable Machine"""
        try:
            print("Loading model...")
            
            # Load the model
            model_path = self.model_url + 'model.json'
            self.model = tf.keras.models.load_model(model_path)
            print("✅ Model loaded successfully!")
            
            # Load metadata to get class names
            metadata_url = self.model_url + 'metadata.json'
            response = requests.get(metadata_url)
            metadata = response.json()
            self.class_names = metadata['labels']
            
            print(f"📝 Classes: {', '.join(self.class_names)}")
            
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            print("Make sure your model URL is correct!")
    
    def preprocess_image(self, image):
        """
        Preprocess image for the model
        
        Args:
            image: OpenCV image (BGR format)
            
        Returns:
            Preprocessed image tensor
        """
        # Convert BGR to RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Resize to model input size (usually 224x224 for Teachable Machine)
        image_resized = cv2.resize(image_rgb, (224, 224))
        
        # Normalize pixel values to [0, 1]
        image_normalized = image_resized.astype(np.float32) / 255.0
        
        # Add batch dimension
        image_batch = np.expand_dims(image_normalized, axis=0)
        
        return image_batch
    
    def predict(self, image):
        """
        Make prediction on an image
        
        Args:
            image: OpenCV image
            
        Returns:
            Dictionary with class names and confidence scores
        """
        if self.model is None:
            return {}
        
        # Preprocess the image
        processed_image = self.preprocess_image(image)
        
        # Make prediction
        predictions = self.model.predict(processed_image, verbose=0)
        
        # Create results dictionary
        results = {}
        for i, class_name in enumerate(self.class_names):
            confidence = float(predictions[0][i]) * 100
            results[class_name] = confidence
        
        return results
    
    def classify_webcam(self):
        """Run real-time classification using webcam"""
        print("\n🎥 Starting webcam classification...")
        print("Press 'q' to quit, 's' to save current frame")
        
        # Initialize webcam
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("❌ Error: Could not open webcam")
            return
        
        # Set webcam resolution
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        frame_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                print("❌ Error: Could not read frame")
                break
            
            # Make prediction every few frames to improve performance
            if frame_count % 10 == 0:
                predictions = self.predict(frame)
                
                # Find the class with highest confidence
                if predictions:
                    top_class = max(predictions, key=predictions.get)
                    top_confidence = predictions[top_class]
                    
                    # Display predictions on frame
                    y_offset = 30
                    for class_name, confidence in predictions.items():
                        color = (0, 255, 0) if class_name == top_class else (255, 255, 255)
                        text = f"{class_name}: {confidence:.1f}%"
                        cv2.putText(frame, text, (10, y_offset), 
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
                        y_offset += 30
                    
                    # Display top prediction prominently
                    emoji_map = {
                        'Happy Face': '😊',
                        'Sad Face': '😢', 
                        'Neutral': '😐'
                    }
                    emoji = emoji_map.get(top_class, '🤔')
                    
                    top_text = f"{emoji} {top_class}"
                    cv2.putText(frame, top_text, (10, frame.shape[0] - 20),
                              cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 3)
            
            frame_count += 1
            
            # Show the frame
            cv2.imshow('Face Expression Classifier', frame)
            
            # Handle key presses
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                filename = f'classified_frame_{int(time.time())}.jpg'
                cv2.imwrite(filename, frame)
                print(f"💾 Saved frame as {filename}")
        
        # Cleanup
        cap.release()
        cv2.destroyAllWindows()
    
    def classify_image(self, image_path):
        """Classify a single image file"""
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                print(f"❌ Error: Could not load image {image_path}")
                return
            
            # Make prediction
            predictions = self.predict(image)
            
            print(f"\n📸 Results for {image_path}:")
            for class_name, confidence in predictions.items():
                emoji_map = {
                    'Happy Face': '😊',
                    'Sad Face': '😢', 
                    'Neutral': '😐'
                }
                emoji = emoji_map.get(class_name, '🤔')
                print(f"{emoji} {class_name}: {confidence:.1f}%")
            
            # Show image with results
            top_class = max(predictions, key=predictions.get)
            cv2.putText(image, f"Prediction: {top_class}", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            cv2.imshow('Classified Image', image)
            cv2.waitKey(0)
            cv2.destroyAllWindows()
            
        except Exception as e:
            print(f"❌ Error classifying image: {e}")

def main():
    print("🎭 Teachable Machine Face Expression Classifier")
    print("=" * 50)
    
    # Get model URL from user
    model_url = input("Enter your Teachable Machine model URL: ").strip()
    
    if not model_url:
        print("❌ No URL provided!")
        return
    
    # Initialize classifier
    classifier = TeachableMachineClassifier(model_url)
    
    if classifier.model is None:
        return
    
    while True:
        print("\n🎯 Choose an option:")
        print("1. Real-time webcam classification")
        print("2. Classify a single image file")
        print("3. Exit")
        
        choice = input("Enter your choice (1-3): ").strip()
        
        if choice == '1':
            classifier.classify_webcam()
        elif choice == '2':
            image_path = input("Enter path to image file: ").strip()
            classifier.classify_image(image_path)
        elif choice == '3':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice!")

if __name__ == "__main__":
    main()