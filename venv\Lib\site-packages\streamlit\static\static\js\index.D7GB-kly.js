import{s as k,r as o,aT as D,ap as H,cy as S,aE as B,j as n,bu as j,bL as O,bv as _,bc as w,bw as K,aF as $,cz as q}from"./index.DvRPFfw6.js";import{u as A}from"./uniqueId.Bm8FHN92.js";import{I as G}from"./InputInstructions.DtUxCBS8.js";import{a as N}from"./useBasicWidgetState.CUSYQZpm.js";import{u as J,a as M,b as Q}from"./useUpdateUiValue.lE5xnYWF.js";import{I as X}from"./input.BL2buuce.js";import"./FormClearHelper.BLEIUk6L.js";import"./inputUtils.CptNuJwn.js";import"./base-input.DeBqm5mN.js";const Y=k("div",{target:"e1o1zy6o0"})("position:relative;");function Z({disabled:a,element:t,widgetMgr:s,fragmentId:u}){const[r,c]=o.useState(()=>W(s,t)??null),[T,y]=D(),[i,p]=o.useState(!1),x=o.useCallback(()=>{c(t.default??null),p(!0)},[t.default]),[V,m]=N({getStateFromWidgetMgr:W,getDefaultStateFromProto:tt,getCurrStateFromProto:et,updateWidgetMgrState:at,element:t,widgetMgr:s,fragmentId:u,onFormCleared:x});J(V,r,c,i);const[F,g]=o.useState(!1),e=H(),[f]=o.useState(()=>A("text_input_")),{placeholder:C,formId:l,icon:d,maxChars:b}=t,h=o.useCallback(()=>{p(!1),m({value:r,fromUi:!0})},[r,m]),E=S({formId:l})?s.allowFormEnterToSubmit(l):i,v=F&&T>e.breakpoints.hideWidgetDetails,z=o.useCallback(()=>{i&&h(),g(!1)},[i,h]),L=o.useCallback(()=>{g(!0)},[]),R=M({formId:l,maxChars:b,setDirty:p,setUiValue:c,setValueWithSource:m}),P=Q(l,h,i,s,u),I=d?.startsWith(":material"),U=I?"lg":"base";return B(Y,{className:"stTextInput","data-testid":"stTextInput",ref:y,children:[n(K,{label:t.label,disabled:a,labelVisibility:j(t.labelVisibility?.value),htmlFor:f,children:t.help&&n(O,{children:n(_,{content:t.help,placement:w.TOP_RIGHT})})}),n(X,{value:r??"",placeholder:C,onBlur:z,onFocus:L,onChange:R,onKeyPress:P,"aria-label":t.label,disabled:a,id:f,type:ot(t),autoComplete:t.autocomplete,startEnhancer:d&&n($,{"data-testid":"stTextInputIcon",iconValue:d,size:U}),overrides:{Input:{style:{fontWeight:e.fontWeights.normal,minWidth:0,lineHeight:e.lineHeights.inputWidget,paddingRight:e.spacing.sm,paddingLeft:e.spacing.md,paddingBottom:e.spacing.sm,paddingTop:e.spacing.sm,"::placeholder":{color:e.colors.fadedText60}}},Root:{props:{"data-testid":"stTextInputRootElement"},style:{height:e.sizes.minElementHeight,borderLeftWidth:e.sizes.borderWidth,borderRightWidth:e.sizes.borderWidth,borderTopWidth:e.sizes.borderWidth,borderBottomWidth:e.sizes.borderWidth,paddingLeft:d?e.spacing.sm:0}},StartEnhancer:{style:{paddingLeft:0,paddingRight:0,minWidth:e.iconSizes.lg,color:I?e.colors.fadedText60:"inherit"}}}}),v&&n(G,{dirty:i,value:r??"",maxLength:b,inForm:S({formId:l}),allowEnterToSubmit:E})]})}function W(a,t){return a.getStringValue(t)??null}function tt(a){return a.default??null}function et(a){return a.value??null}function at(a,t,s,u){t.setStringValue(a,s.value,{fromUi:s.fromUi},u)}function ot(a){return a.type===q.Type.PASSWORD?"password":"text"}const mt=o.memo(Z);export{mt as default};
