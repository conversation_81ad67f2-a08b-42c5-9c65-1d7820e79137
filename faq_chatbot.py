import streamlit as st
import json
import nltk
import string
from nltk.corpus import stopwords
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

# Download NLTK stopwords
nltk.download("stopwords")

# Load FAQs
with open("faqs.json", "r") as f:
    faq_data = json.load(f)

faq_questions = [faq["question"] for faq in faq_data]
faq_answers = [faq["answer"] for faq in faq_data]

# Preprocessing function
def preprocess(text):
    text = text.lower()
    tokens = text.split()
    tokens = [word.strip(string.punctuation) for word in tokens if word not in stopwords.words("english")]
    return " ".join(tokens)

preprocessed_questions = [preprocess(q) for q in faq_questions]

# TF-IDF Vectorization
vectorizer = TfidfVectorizer()
tfidf_matrix = vectorizer.fit_transform(preprocessed_questions)

# Streamlit UI
st.set_page_config(page_title="FAQ Chatbot", layout="centered")
st.title("📚 FAQ Chatbot")

user_input = st.text_input("Ask me something:")

if st.button("Get Answer"):
    if user_input.strip():
        user_processed = preprocess(user_input)
        user_vec = vectorizer.transform([user_processed])
        
        similarity = cosine_similarity(user_vec, tfidf_matrix)
        best_match_index = similarity.argmax()

        if similarity[0][best_match_index] > 0.1:
            st.success(f"💡 {faq_answers[best_match_index]}")
        else:
            st.warning("Sorry, I don't understand that. Please try another question.")
    else:
        st.error("Please type a question before clicking the button.")
