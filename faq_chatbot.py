import streamlit as st
import json
import nltk
import string
from nltk.corpus import stopwords
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

# Download NLTK stopwords
nltk.download("stopwords", quiet=True)

# Load FAQs from JSON file
try:
    with open("faqs.json", "r", encoding="utf-8") as f:
        faq_data = json.load(f)

    faq_questions = [faq["question"] for faq in faq_data]
    faq_answers = [faq["answer"] for faq in faq_data]

    print(f"Loaded {len(faq_data)} FAQ entries successfully!")

except FileNotFoundError:
    st.error("❌ faqs.json file not found! Please make sure the file exists in the same directory.")
    st.stop()
except json.JSONDecodeError:
    st.error("❌ Error reading faqs.json file! Please check the JSON format.")
    st.stop()
except Exception as e:
    st.error(f"❌ Error loading FAQ data: {str(e)}")
    st.stop()

# Preprocessing function
def preprocess(text):
    """Clean and preprocess text for better matching"""
    if not text:
        return ""

    text = text.lower()
    # Remove punctuation and split into tokens
    tokens = text.split()
    # Remove stopwords and punctuation, keep only meaningful words
    tokens = [word.strip(string.punctuation) for word in tokens
              if word.strip(string.punctuation) and word.lower() not in stopwords.words("english")]
    return " ".join(tokens)

# Preprocess all FAQ questions
preprocessed_questions = [preprocess(q) for q in faq_questions]

# TF-IDF Vectorization for similarity matching
vectorizer = TfidfVectorizer(ngram_range=(1, 2), max_features=1000)
tfidf_matrix = vectorizer.fit_transform(preprocessed_questions)

# Function to find the best answer
def find_best_answer(user_question, threshold=0.1):
    """Find the best matching answer for user question"""
    if not user_question.strip():
        return None, 0

    user_processed = preprocess(user_question)
    if not user_processed:
        return None, 0

    user_vec = vectorizer.transform([user_processed])
    similarity_scores = cosine_similarity(user_vec, tfidf_matrix)
    best_match_index = similarity_scores.argmax()
    best_score = similarity_scores[0][best_match_index]

    if best_score > threshold:
        return faq_answers[best_match_index], best_score
    else:
        return None, best_score

# Streamlit UI Configuration
st.set_page_config(
    page_title="FAQ Chatbot",
    page_icon="🤖",
    layout="centered",
    initial_sidebar_state="expanded"
)

# Main UI
st.title("🤖 Intelligent FAQ Chatbot")
st.markdown("---")

# Sidebar with FAQ list
with st.sidebar:
    st.header("📋 Available FAQs")
    st.markdown("**Sample questions you can ask:**")
    for i, question in enumerate(faq_questions[:5], 1):
        st.markdown(f"{i}. {question}")
    if len(faq_questions) > 5:
        st.markdown(f"... and {len(faq_questions) - 5} more!")

    st.markdown("---")
    st.markdown(f"**Total FAQs:** {len(faq_questions)}")

# Main chat interface
st.markdown("### 💬 Ask me anything!")
user_input = st.text_input("Type your question here:", placeholder="e.g., What is AI?")

col1, col2 = st.columns([1, 4])
with col1:
    ask_button = st.button("🔍 Get Answer", type="primary")
with col2:
    if st.button("🔄 Clear"):
        st.rerun()

if ask_button:
    if user_input.strip():
        with st.spinner("🔍 Searching for the best answer..."):
            answer, confidence = find_best_answer(user_input)

            if answer:
                st.success("✅ **Answer Found!**")
                st.markdown(f"**Q:** {user_input}")
                st.markdown(f"**A:** {answer}")

                # Show confidence score
                confidence_percentage = confidence * 100
                if confidence_percentage > 70:
                    st.info(f"🎯 Confidence: {confidence_percentage:.1f}% (High)")
                elif confidence_percentage > 40:
                    st.info(f"🎯 Confidence: {confidence_percentage:.1f}% (Medium)")
                else:
                    st.info(f"🎯 Confidence: {confidence_percentage:.1f}% (Low)")
            else:
                st.warning("❓ **Sorry, I couldn't find a good match for your question.**")
                st.markdown("**Suggestions:**")
                st.markdown("- Try rephrasing your question")
                st.markdown("- Check the sidebar for available topics")
                st.markdown("- Use simpler keywords")
    else:
        st.error("❌ Please type a question before clicking the button.")

# Footer
st.markdown("---")
st.markdown("*💡 Tip: Try asking questions related to AI, machine learning, or programming!*")
