import os
import shutil
import tkinter as tk
from tkinter import filedialog, messagebox

# Function to select source folder
def select_source():
    folder = filedialog.askdirectory()
    if folder:
        source_var.set(folder)

# Function to select destination folder
def select_destination():
    folder = filedialog.askdirectory()
    if folder:
        destination_var.set(folder)

# Function to move .jpg files
def move_files():
    source = source_var.get()
    destination = destination_var.get()

    if not source or not destination:
        messagebox.showerror("Error", "Please select both source and destination folders.")
        return

    os.makedirs(destination, exist_ok=True)
    moved_count = 0

    for file_name in os.listdir(source):
        if file_name.lower().endswith(".jpg"):
            shutil.move(os.path.join(source, file_name),
                        os.path.join(destination, file_name))
            moved_count += 1

    messagebox.showinfo("Done", f"Moved {moved_count} JPG files successfully!")

# Create main window
root = tk.Tk()
root.title("JPG File Mover")
root.geometry("800x400")
root.resizable(False, False)

# Variables to store folder paths
source_var = tk.StringVar()
destination_var = tk.StringVar()

# UI Elements
tk.Label(root, text="Source Folder:").pack(pady=5)
tk.Entry(root, textvariable=source_var, width=40).pack()
tk.Button(root, text="Browse", command=select_source).pack()

tk.Label(root, text="Destination Folder:").pack(pady=5)
tk.Entry(root, textvariable=destination_var, width=40).pack()
tk.Button(root, text="Browse", command=select_destination).pack()

tk.Button(root, text="Move JPG Files", command=move_files, bg="green", fg="white").pack(pady=20)

# Run app
root.mainloop()
