import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import streamlit as st

# --- Streamlit UI ---
st.title("📧 Real-Time Email Automation System")
st.write("Send the same email to multiple recipients at once.")

# Email sender credentials
sender_email = st.text_input("Your Email Address (Gmail recommended)")
app_password = st.text_input("Your App Password", type="password")

# Predefined recipient list (you can edit)
recipients = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    # Add up to 20 or more emails here
]

# Email details
subject = st.text_input("Email Subject")
message_body = st.text_area("Email Message")

if st.button("Send Email"):
    if not sender_email or not app_password or not subject or not message_body:
        st.error("Please fill all the fields.")
    else:
        try:
            # Set up SMTP server
            server = smtplib.SMTP("smtp.gmail.com", 587)
            server.starttls()
            server.login(sender_email, app_password)

            for receiver_email in recipients:
                msg = MIMEMultipart()
                msg["From"] = sender_email
                msg["To"] = receiver_email
                msg["Subject"] = subject

                msg.attach(MIMEText(message_body, "plain"))

                server.sendmail(sender_email, receiver_email, msg.as_string())

            server.quit()
            st.success(f"Email sent successfully to {len(recipients)} recipients!")

        except Exception as e:
            st.error(f"Error: {e}")
