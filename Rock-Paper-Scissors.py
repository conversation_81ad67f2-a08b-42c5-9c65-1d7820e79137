import tkinter as tk
from tkinter import messagebox
import random

class SimpleRockPaperScissors:
    def __init__(self, root):
        self.root = root
        self.root.title("Rock Paper Scissors")
        self.root.geometry("400x300")
        
        # Game state
        self.player_score = 0
        self.computer_score = 0
        self.choices = ['Rock', 'Paper', 'Scissors']
        
        self.setup_ui()
    
    def setup_ui(self):
        # Title
        title = tk.Label(self.root, text="Rock Paper Scissors", font=('Arial', 16))
        title.pack(pady=10)
        
        # Scores
        self.score_label = tk.Label(self.root, text="Player: 0  |  Computer: 0", font=('Arial', 12))
        self.score_label.pack(pady=5)
        
        # Result display
        self.result_label = tk.Label(self.root, text="Choose Rock, Paper or Scissors", font=('Arial', 11))
        self.result_label.pack(pady=10)
        
        # Buttons frame
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=10)
        
        # Choice buttons
        rock_btn = tk.Button(button_frame, text="Rock", width=10, command=lambda: self.play('Rock'))
        rock_btn.pack(side='left', padx=5)
        
        paper_btn = tk.Button(button_frame, text="Paper", width=10, command=lambda: self.play('Paper'))
        paper_btn.pack(side='left', padx=5)
        
        scissors_btn = tk.Button(button_frame, text="Scissors", width=10, command=lambda: self.play('Scissors'))
        scissors_btn.pack(side='left', padx=5)
        
        # Control buttons frame
        control_frame = tk.Frame(self.root)
        control_frame.pack(pady=20)
        
        # Reset button
        reset_btn = tk.Button(control_frame, text="Reset", width=10, command=self.reset_game)
        reset_btn.pack(side='left', padx=5)
        
        # Quit button
        quit_btn = tk.Button(control_frame, text="Quit", width=10, command=self.root.quit)
        quit_btn.pack(side='left', padx=5)
    
    def play(self, player_choice):
        # Computer makes random choice
        computer_choice = random.choice(self.choices)
        
        # Determine winner
        result = self.get_winner(player_choice, computer_choice)
        
        # Update scores
        if result == "Player wins":
            self.player_score += 1
        elif result == "Computer wins":
            self.computer_score += 1
        
        # Update display
        self.result_label.config(text=f"You: {player_choice} | Computer: {computer_choice}\n{result}")
        self.score_label.config(text=f"Player: {self.player_score}  |  Computer: {self.computer_score}")
    
    def get_winner(self, player, computer):
        if player == computer:
            return "It's a tie!"
        elif (player == "Rock" and computer == "Scissors") or \
             (player == "Paper" and computer == "Rock") or \
             (player == "Scissors" and computer == "Paper"):
            return "Player wins!"
        else:
            return "Computer wins!"
    
    def reset_game(self):
        self.player_score = 0
        self.computer_score = 0
        self.score_label.config(text="Player: 0  |  Computer: 0")
        self.result_label.config(text="Choose Rock, Paper or Scissors")

# Run the game
if __name__ == "__main__":
    root = tk.Tk()
    game = SimpleRockPaperScissors(root)
    root.mainloop()