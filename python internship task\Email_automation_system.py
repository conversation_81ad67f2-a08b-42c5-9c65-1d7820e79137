import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import List, Dict
import time
from datetime import datetime

class EmailAutomationSystem:
    def __init__(self):
        # Predefined list of 20 email addresses for testing
        self.predefined_emails = [
            "<EMAIL>", "<EMAIL>", "<EMAIL>",
            "<EMAIL>", "<EMAIL>", "<EMAIL>",
            "<EMAIL>", "<EMAIL>", "<EMAIL>",
            "<EMAIL>", "<EMAIL>", "<EMAIL>",
            "<EMAIL>", "<EMAIL>", "<EMAIL>",
            "<EMAIL>", "<EMAIL>", "<EMAIL>",
            "<EMAIL>", "<EMAIL>"
        ]
        
        # Email configuration
        self.smtp_servers = {
            'gmail': {'server': 'smtp.gmail.com', 'port': 587},
            'outlook': {'server': 'smtp-mail.outlook.com', 'port': 587},
            'yahoo': {'server': 'smtp.mail.yahoo.com', 'port': 587}
        }
        
    def setup_email_config(self, sender_email: str, sender_password: str, provider: str = 'gmail'):
        """
        Configure email settings
        
        Args:
            sender_email: Your email address
            sender_password: Your app password (not regular password)
            provider: Email provider ('gmail', 'outlook', 'yahoo')
        """
        self.sender_email = sender_email
        self.sender_password = sender_password
        self.provider = provider.lower()
        
        if self.provider not in self.smtp_servers:
            raise ValueError("Unsupported email provider. Use 'gmail', 'outlook', or 'yahoo'")
    
    def create_email_message(self, subject: str, message_body: str, recipient_email: str) -> MIMEMultipart:
        """
        Create email message with proper formatting
        
        Args:
            subject: Email subject line
            message_body: Main email content
            recipient_email: Recipient's email address
            
        Returns:
            MIMEMultipart: Formatted email message
        """
        message = MIMEMultipart()
        message["From"] = self.sender_email
        message["To"] = recipient_email
        message["Subject"] = subject
        
        # Add body to email
        message.attach(MIMEText(message_body, "plain"))
        
        return message
    
    def send_bulk_emails(self, subject: str, message_body: str, 
                        recipient_emails: List[str] = None, 
                        use_predefined: bool = True,
                        batch_size: int = 10,
                        delay_between_batches: int = 2) -> Dict:
        """
        Send emails to multiple recipients
        
        Args:
            subject: Email subject
            message_body: Email content
            recipient_emails: List of recipient emails (optional)
            use_predefined: Whether to use predefined email list
            batch_size: Number of emails to send per batch
            delay_between_batches: Delay in seconds between batches
            
        Returns:
            Dict: Results with success/failure counts and details
        """
        
        # Determine which email list to use
        if use_predefined:
            emails_to_send = self.predefined_emails
        else:
            emails_to_send = recipient_emails if recipient_emails else []
        
        if not emails_to_send:
            return {"error": "No recipient emails provided"}
        
        # Results tracking
        results = {
            "total_emails": len(emails_to_send),
            "successful": 0,
            "failed": 0,
            "failed_emails": [],
            "start_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "end_time": None
        }
        
        try:
            # Get SMTP configuration
            smtp_config = self.smtp_servers[self.provider]
            
            # Create secure SSL context
            context = ssl.create_default_context()
            
            # Connect to server and send emails
            with smtplib.SMTP(smtp_config['server'], smtp_config['port']) as server:
                server.ehlo()
                server.starttls(context=context)
                server.ehlo()
                server.login(self.sender_email, self.sender_password)
                
                # Send emails in batches
                for i in range(0, len(emails_to_send), batch_size):
                    batch = emails_to_send[i:i + batch_size]
                    
                    print(f"Sending batch {i//batch_size + 1}: {len(batch)} emails")
                    
                    for email in batch:
                        try:
                            # Create message for each recipient
                            message = self.create_email_message(subject, message_body, email)
                            
                            # Send email
                            text = message.as_string()
                            server.sendmail(self.sender_email, email, text)
                            
                            results["successful"] += 1
                            print(f"✓ Email sent successfully to: {email}")
                            
                        except Exception as e:
                            results["failed"] += 1
                            results["failed_emails"].append({"email": email, "error": str(e)})
                            print(f"✗ Failed to send to {email}: {str(e)}")
                    
                    # Delay between batches to avoid rate limiting
                    if i + batch_size < len(emails_to_send):
                        print(f"Waiting {delay_between_batches} seconds before next batch...")
                        time.sleep(delay_between_batches)
        
        except Exception as e:
            results["error"] = f"SMTP connection failed: {str(e)}"
            return results
        
        results["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        return results
    
    def get_predefined_emails(self) -> List[str]:
        """Return the list of predefined emails"""
        return self.predefined_emails.copy()
    
    def add_custom_emails(self, custom_emails: List[str]) -> List[str]:
        """
        Add custom emails to the predefined list
        
        Args:
            custom_emails: List of additional email addresses
            
        Returns:
            List[str]: Combined list of emails
        """
        return self.predefined_emails + custom_emails

# Example usage and testing
def main():
    # Initialize the email system
    email_system = EmailAutomationSystem()
    
    # Example configuration (replace with your actual credentials)
    try:
        # IMPORTANT: For Gmail, use an "App Password" instead of your regular password
        # Go to Google Account settings > Security > App passwords to generate one
        email_system.setup_email_config(
            sender_email="<EMAIL>",
            sender_password="your_app_password",
            provider="gmail"
        )
        
        # Email content
        subject = "Test Bulk Email"
        message_body = """
        Hello!
        
        This is a test email sent using our automated email system.
        This message is being sent to multiple recipients simultaneously.
        
        Best regards,
        Email Automation System
        """
        
        # Send emails to predefined list
        print("Starting bulk email sending...")
        results = email_system.send_bulk_emails(
            subject=subject,
            message_body=message_body,
            use_predefined=True,
            batch_size=5,  # Send 5 emails per batch
            delay_between_batches=1  # 1 second delay between batches
        )
        
        # Print results
        print("\n" + "="*50)
        print("EMAIL SENDING RESULTS")
        print("="*50)
        print(f"Total emails: {results['total_emails']}")
        print(f"Successful: {results['successful']}")
        print(f"Failed: {results['failed']}")
        print(f"Start time: {results['start_time']}")
        print(f"End time: {results['end_time']}")
        
        if results['failed_emails']:
            print("\nFailed emails:")
            for failed in results['failed_emails']:
                print(f"  - {failed['email']}: {failed['error']}")
    
    except Exception as e:
        print(f"Error: {e}")
        print("\nPlease make sure to:")
        print("1. Replace '<EMAIL>' with your actual email")
        print("2. Replace 'your_app_password' with your actual app password")
        print("3. Enable 2FA and generate an app password for Gmail")

if __name__ == "__main__":
    main()