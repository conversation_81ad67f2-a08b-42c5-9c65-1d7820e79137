import streamlit as st
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import time

# Predefined list of 20 emails
PREDEFINED_EMAILS = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
]

def send_email(sender_email, sender_password, recipient_emails, subject, message):
    try:
        # Set up the SMTP server
        with smtplib.SMTP('smtp.gmail.com', 587) as server:
            server.starttls()
            server.login(sender_email, sender_password)
            
            for recipient in recipient_emails:
                # Create message container
                msg = MIMEMultipart()
                msg['From'] = sender_email
                msg['To'] = recipient
                msg['Subject'] = subject
                
                # Add message body
                msg.attach(MIMEText(message, 'plain'))
                
                # Send the email
                server.send_message(msg)
                
                # Small delay to avoid overwhelming the server
                time.sleep(0.5)
                
        return True, "Emails sent successfully!"
    except Exception as e:
        return False, f"Error sending emails: {str(e)}"

def main():
    st.title("📧 Bulk Email Sender")
    st.write("Send the same message to multiple recipients at once")
    
    # Sender credentials section
    st.subheader("Sender Information")
    sender_email = st.text_input("Your Email Address")
    sender_password = st.text_input("Your Email Password", type="password")
    
    # Recipient selection
    st.subheader("Recipients")
    use_predefined = st.checkbox("Use predefined email list (20 recipients)", value=True)
    
    if use_predefined:
        recipient_emails = PREDEFINED_EMAILS
        st.info(f"Using predefined list of {len(PREDEFINED_EMAILS)} emails")
    else:
        custom_emails = st.text_area("Enter recipient emails (one per line)")
        recipient_emails = [email.strip() for email in custom_emails.split('\n') if email.strip()]
    
    # Email composition
    st.subheader("Email Content")
    subject = st.text_input("Subject")
    message = st.text_area("Message", height=200)
    
    # Send button
    if st.button("Send Emails"):
        if not sender_email or not sender_password:
            st.error("Please enter your email and password")
        elif not recipient_emails:
            st.error("No recipients specified")
        elif not subject or not message:
            st.error("Please enter both subject and message")
        else:
            with st.spinner(f"Sending emails to {len(recipient_emails)} recipients..."):
                success, result = send_email(
                    sender_email,
                    sender_password,
                    recipient_emails,
                    subject,
                    message
                )
                
                if success:
                    st.success(result)
                else:
                    st.error(result)

if __name__ == "__main__":
    main()