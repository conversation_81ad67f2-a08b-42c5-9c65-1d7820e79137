import time
import random
import re
from typing import List, Tuple, Dict
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmailAutomationSystem:
    """
    Email Automation System - Handles email validation, sending simulation, and progress tracking
    """
    
    def __init__(self):
        self.predefined_emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
    
    def get_predefined_emails(self) -> List[str]:
        """Return the list of predefined email addresses"""
        return self.predefined_emails.copy()
    
    def validate_email(self, email: str) -> bool:
        """
        Validate email format using regex
        
        Args:
            email (str): Email address to validate
            
        Returns:
            bool: True if email is valid, False otherwise
        """
        if not email or not isinstance(email, str):
            return False
        
        email_regex = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
        return re.match(email_regex, email.strip()) is not None
    
    def validate_inputs(self, sender_email: str, subject: str, message: str, selected_emails: List[str]) -> Tuple[bool, str]:
        """
        Validate all input parameters before sending emails
        
        Args:
            sender_email (str): Sender's email address
            subject (str): Email subject
            message (str): Email message body
            selected_emails (List[str]): List of recipient emails
            
        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        # Check if all required fields are filled
        if not sender_email or not sender_email.strip():
            return False, "Sender email is required."
        
        if not subject or not subject.strip():
            return False, "Email subject is required."
        
        if not message or not message.strip():
            return False, "Email message is required."
        
        # Validate sender email format
        if not self.validate_email(sender_email):
            return False, "Please enter a valid sender email address."
        
        # Check if recipients are selected
        if not selected_emails or len(selected_emails) == 0:
            return False, "Please select at least one recipient."
        
        return True, ""
    
    async def simulate_email_send(self, recipient_email: str, subject: str, message: str, sender_email: str) -> bool:
        """
        Simulate sending an email (replace with actual email service integration)
        
        Args:
            recipient_email (str): Recipient's email address
            subject (str): Email subject
            message (str): Email message body
            sender_email (str): Sender's email address
            
        Returns:
            bool: True if successful, False if failed
        """
        # Simulate network delay
        delay = random.uniform(0.5, 1.5)  # Random delay between 0.5-1.5 seconds
        time.sleep(delay)
        
        # Log email details (in production, this would send actual emails)
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        logger.info(f"""
📧 EMAIL SENT:
From: {sender_email}
To: {recipient_email}
Subject: {subject}
Message: {message[:100]}{'...' if len(message) > 100 else ''}
Timestamp: {timestamp}
        """)
        
        # Simulate occasional failures (90% success rate)
        success_rate = 0.9
        return random.random() < success_rate
    
    def send_emails_batch(self, sender_email: str, subject: str, message: str, selected_emails: List[str]) -> Dict:
        """
        Send emails to multiple recipients with progress tracking
        
        Args:
            sender_email (str): Sender's email address
            subject (str): Email subject
            message (str): Email message body
            selected_emails (List[str]): List of recipient emails
            
        Returns:
            Dict: Results containing success count, failure count, and detailed results
        """
        results = {
            'success_count': 0,
            'failure_count': 0,
            'total_emails': len(selected_emails),
            'detailed_results': [],
            'progress_updates': []
        }
        
        try:
            for i, email in enumerate(selected_emails):
                progress = ((i + 1) / len(selected_emails)) * 100
                results['progress_updates'].append(progress)
                
                try:
                    # In an async context, you would use await here
                    # For now, we'll use the synchronous version
                    success = self._sync_simulate_email_send(email, subject, message, sender_email)
                    
                    if success:
                        results['success_count'] += 1
                        results['detailed_results'].append({
                            'email': email,
                            'status': 'success',
                            'message': 'Email sent successfully'
                        })
                    else:
                        results['failure_count'] += 1
                        results['detailed_results'].append({
                            'email': email,
                            'status': 'failed',
                            'message': 'Failed to send email'
                        })
                        
                except Exception as e:
                    results['failure_count'] += 1
                    results['detailed_results'].append({
                        'email': email,
                        'status': 'error',
                        'message': f'Error: {str(e)}'
                    })
                    logger.error(f"Failed to send email to {email}: {str(e)}")
            
        except Exception as e:
            logger.error(f"Batch email sending error: {str(e)}")
            raise e
        
        return results
    
    def _sync_simulate_email_send(self, recipient_email: str, subject: str, message: str, sender_email: str) -> bool:
        """
        Synchronous version of email sending simulation
        
        Args:
            recipient_email (str): Recipient's email address
            subject (str): Email subject
            message (str): Email message body
            sender_email (str): Sender's email address
            
        Returns:
            bool: True if successful, False if failed
        """
        # Simulate network delay
        delay = random.uniform(0.1, 0.3)  # Shorter delay for better UI experience
        time.sleep(delay)
        
        # Log email details
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        logger.info(f"📧 Sending email from {sender_email} to {recipient_email} - {timestamp}")
        
        # Simulate occasional failures (90% success rate)
        success_rate = 0.9
        return random.random() < success_rate
    
    def get_sample_data(self) -> Dict[str, str]:
        """
        Get sample data for testing purposes
        
        Returns:
            Dict[str, str]: Sample sender email, subject, and message
        """
        return {
            'sender_email': '<EMAIL>',
            'subject': 'Important Update',
            'message': '''Hello,

This is an automated email sent through our email automation system. Thank you for your attention.

Best regards,
Your Team'''
        }
    
    def format_results_message(self, results: Dict) -> Tuple[str, str]:
        """
        Format the results into a user-friendly message
        
        Args:
            results (Dict): Results from send_emails_batch
            
        Returns:
            Tuple[str, str]: (message, message_type) where message_type is 'success', 'warning', or 'error'
        """
        success_count = results['success_count']
        failure_count = results['failure_count']
        total_count = results['total_emails']
        
        if failure_count == 0:
            return f"✅ Successfully sent {success_count} emails!", "success"
        elif success_count > 0:
            return f"⚠️ Sent {success_count} emails successfully, {failure_count} failed.", "warning"
        else:
            return f"❌ Failed to send all {total_count} emails. Please try again.", "error"